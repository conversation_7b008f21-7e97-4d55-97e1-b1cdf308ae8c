import { createContext, useContext, useReducer, useEffect } from 'react';

// 遊戲常數
export const GAME_CONSTANTS = {
  PACMAN_BASE_SPEED: 60,
  GHOST_MOVE_SPEED_METERS_PER_SECOND: 60,
  MAX_MAP_ZOOM: 18,
  MAX_DELTA_TIME: 100,
  NUMBER_OF_GHOSTS: 7,
  BG_PACMAN_SPEED: 40,
  BG_GHOST_SPEED: 30,
  BG_NUMBER_OF_GHOSTS: 3
};

// 地圖設定
export const MAP_CONFIGS = [
  { name: "台北市中心", center: [25.0330, 121.5654], zoom: GAME_CONSTANTS.MAX_MAP_ZOOM },
  { name: "台中市區", center: [24.1477, 120.6736], zoom: GAME_CONSTANTS.MAX_MAP_ZOOM },
  { name: "高雄市區", center: [22.6273, 120.3014], zoom: GAME_CONSTANTS.MAX_MAP_ZOOM }
];

// 初始遊戲狀態
const initialGameState = {
  // 地圖和遊戲物件
  map: null,
  pacman: null,
  pacmanLevelStartPoint: null,
  pacmanMovement: {
    isMoving: false,
    startPositionLatLng: null,
    destinationNodeLatLng: null,
    totalDistanceToDestinationNode: 0,
    distanceTraveledThisSegment: 0,
    lastIntendedDirectionKey: null,
    currentFacingDirection: 'left'
  },
  ghosts: [],
  dots: [],
  powerPellets: [],
  
  // 遊戲狀態
  score: 0,
  lives: 3,
  level: 1,
  gameTime: 600,
  isPaused: false,
  isGameOver: false,
  isLosingLife: false,
  currentMapIndex: 0,
  gameTimer: null,
  powerMode: false,
  powerModeTimer: null,
  dotsCollected: 0,
  totalDots: 0,
  canMove: false,
  
  // 地圖相關
  roadNetwork: [],
  validPositions: [],
  roadLayers: [],
  adjacencyList: new Map(),
  ghostSpawnPoints: [],
  baseScatterPoints: [],
  
  // 遊戲設定
  gameSpeedMultiplier: 1,
  pacmanSpeedMultiplier: 1.0,
  godMode: false,
  autoPilotMode: false,
  cleverMode: false,
  autoPilotPath: [],
  autoPilotTarget: null,
  
  // UI 狀態
  currentScreen: 'start', // 'start', 'mapSelection', 'game', 'pause', 'gameOver'
  showInstructions: false,
  showLeaderboard: false,
  isDevConsoleOpen: false,
  
  // 排行榜
  leaderboard: []
};

// 遊戲動作類型
export const GAME_ACTIONS = {
  SET_MAP: 'SET_MAP',
  SET_PACMAN: 'SET_PACMAN',
  UPDATE_PACMAN_MOVEMENT: 'UPDATE_PACMAN_MOVEMENT',
  SET_GHOSTS: 'SET_GHOSTS',
  UPDATE_GHOST: 'UPDATE_GHOST',
  SET_DOTS: 'SET_DOTS',
  COLLECT_DOT: 'COLLECT_DOT',
  SET_POWER_PELLETS: 'SET_POWER_PELLETS',
  COLLECT_POWER_PELLET: 'COLLECT_POWER_PELLET',
  UPDATE_SCORE: 'UPDATE_SCORE',
  UPDATE_LIVES: 'UPDATE_LIVES',
  UPDATE_LEVEL: 'UPDATE_LEVEL',
  UPDATE_GAME_TIME: 'UPDATE_GAME_TIME',
  SET_PAUSED: 'SET_PAUSED',
  SET_GAME_OVER: 'SET_GAME_OVER',
  SET_LOSING_LIFE: 'SET_LOSING_LIFE',
  SET_CURRENT_MAP_INDEX: 'SET_CURRENT_MAP_INDEX',
  SET_POWER_MODE: 'SET_POWER_MODE',
  SET_CAN_MOVE: 'SET_CAN_MOVE',
  SET_ROAD_NETWORK: 'SET_ROAD_NETWORK',
  SET_VALID_POSITIONS: 'SET_VALID_POSITIONS',
  SET_ADJACENCY_LIST: 'SET_ADJACENCY_LIST',
  SET_GHOST_SPAWN_POINTS: 'SET_GHOST_SPAWN_POINTS',
  SET_CURRENT_SCREEN: 'SET_CURRENT_SCREEN',
  TOGGLE_INSTRUCTIONS: 'TOGGLE_INSTRUCTIONS',
  TOGGLE_LEADERBOARD: 'TOGGLE_LEADERBOARD',
  TOGGLE_DEV_CONSOLE: 'TOGGLE_DEV_CONSOLE',
  UPDATE_LEADERBOARD: 'UPDATE_LEADERBOARD',
  RESET_GAME: 'RESET_GAME',
  SET_GOD_MODE: 'SET_GOD_MODE',
  SET_PACMAN_SPEED_MULTIPLIER: 'SET_PACMAN_SPEED_MULTIPLIER',
  SET_AUTO_PILOT_MODE: 'SET_AUTO_PILOT_MODE',
  SET_CLEVER_MODE: 'SET_CLEVER_MODE'
};

// 遊戲狀態 reducer
function gameReducer(state, action) {
  switch (action.type) {
    case GAME_ACTIONS.SET_MAP:
      return { ...state, map: action.payload };
    
    case GAME_ACTIONS.SET_PACMAN:
      return { ...state, pacman: action.payload };
    
    case GAME_ACTIONS.UPDATE_PACMAN_MOVEMENT:
      return { 
        ...state, 
        pacmanMovement: { ...state.pacmanMovement, ...action.payload } 
      };
    
    case GAME_ACTIONS.SET_GHOSTS:
      return { ...state, ghosts: action.payload };
    
    case GAME_ACTIONS.UPDATE_GHOST:
      return {
        ...state,
        ghosts: state.ghosts.map((ghost, index) => 
          index === action.payload.index ? { ...ghost, ...action.payload.data } : ghost
        )
      };
    
    case GAME_ACTIONS.SET_DOTS:
      return { ...state, dots: action.payload, totalDots: action.payload.length };
    
    case GAME_ACTIONS.COLLECT_DOT:
      return {
        ...state,
        dots: state.dots.filter((_, index) => index !== action.payload),
        dotsCollected: state.dotsCollected + 1,
        score: state.score + 20
      };
    
    case GAME_ACTIONS.SET_POWER_PELLETS:
      return { ...state, powerPellets: action.payload };
    
    case GAME_ACTIONS.COLLECT_POWER_PELLET:
      return {
        ...state,
        powerPellets: state.powerPellets.filter((_, index) => index !== action.payload),
        score: state.score + 50,
        powerMode: true
      };
    
    case GAME_ACTIONS.UPDATE_SCORE:
      return { ...state, score: action.payload };
    
    case GAME_ACTIONS.UPDATE_LIVES:
      return { ...state, lives: action.payload };
    
    case GAME_ACTIONS.UPDATE_LEVEL:
      return { ...state, level: action.payload };
    
    case GAME_ACTIONS.UPDATE_GAME_TIME:
      return { ...state, gameTime: action.payload };
    
    case GAME_ACTIONS.SET_PAUSED:
      return { ...state, isPaused: action.payload };
    
    case GAME_ACTIONS.SET_GAME_OVER:
      return { ...state, isGameOver: action.payload };
    
    case GAME_ACTIONS.SET_LOSING_LIFE:
      return { ...state, isLosingLife: action.payload };
    
    case GAME_ACTIONS.SET_CURRENT_MAP_INDEX:
      return { ...state, currentMapIndex: action.payload };
    
    case GAME_ACTIONS.SET_POWER_MODE:
      return { ...state, powerMode: action.payload };
    
    case GAME_ACTIONS.SET_CAN_MOVE:
      return { ...state, canMove: action.payload };
    
    case GAME_ACTIONS.SET_ROAD_NETWORK:
      return { ...state, roadNetwork: action.payload };
    
    case GAME_ACTIONS.SET_VALID_POSITIONS:
      return { ...state, validPositions: action.payload };
    
    case GAME_ACTIONS.SET_ADJACENCY_LIST:
      return { ...state, adjacencyList: action.payload };
    
    case GAME_ACTIONS.SET_GHOST_SPAWN_POINTS:
      return { ...state, ghostSpawnPoints: action.payload };
    
    case GAME_ACTIONS.SET_CURRENT_SCREEN:
      return { ...state, currentScreen: action.payload };
    
    case GAME_ACTIONS.TOGGLE_INSTRUCTIONS:
      return { 
        ...state, 
        showInstructions: !state.showInstructions,
        showLeaderboard: false 
      };
    
    case GAME_ACTIONS.TOGGLE_LEADERBOARD:
      return { 
        ...state, 
        showLeaderboard: !state.showLeaderboard,
        showInstructions: false 
      };
    
    case GAME_ACTIONS.TOGGLE_DEV_CONSOLE:
      return { ...state, isDevConsoleOpen: !state.isDevConsoleOpen };
    
    case GAME_ACTIONS.UPDATE_LEADERBOARD:
      return { ...state, leaderboard: action.payload };
    
    case GAME_ACTIONS.RESET_GAME:
      return {
        ...initialGameState,
        leaderboard: state.leaderboard,
        currentMapIndex: state.currentMapIndex
      };

    case GAME_ACTIONS.SET_GOD_MODE:
      return { ...state, godMode: action.payload };

    case GAME_ACTIONS.SET_PACMAN_SPEED_MULTIPLIER:
      return { ...state, pacmanSpeedMultiplier: action.payload };

    case GAME_ACTIONS.SET_AUTO_PILOT_MODE:
      return { ...state, autoPilotMode: action.payload };

    case GAME_ACTIONS.SET_CLEVER_MODE:
      return { ...state, cleverMode: action.payload };

    default:
      return state;
  }
}

// Context
const GameContext = createContext();

// Provider 組件
export function GameProvider({ children }) {
  const [gameState, dispatch] = useReducer(gameReducer, initialGameState);

  // 載入排行榜
  useEffect(() => {
    const savedLeaderboard = localStorage.getItem('pacMapLeaderboard');
    if (savedLeaderboard) {
      dispatch({
        type: GAME_ACTIONS.UPDATE_LEADERBOARD,
        payload: JSON.parse(savedLeaderboard)
      });
    }
  }, []);

  // 儲存排行榜
  useEffect(() => {
    localStorage.setItem('pacMapLeaderboard', JSON.stringify(gameState.leaderboard));
  }, [gameState.leaderboard]);

  return (
    <GameContext.Provider value={{ gameState, dispatch }}>
      {children}
    </GameContext.Provider>
  );
}

// Hook 來使用 context
export function useGame() {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  return context;
}
