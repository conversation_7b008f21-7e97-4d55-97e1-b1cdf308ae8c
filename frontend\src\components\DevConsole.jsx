import React, { useState, useRef, useEffect } from 'react';
import { useGame, GAME_ACTIONS } from '../contexts/GameContext';

function DevConsole() {
  const { gameState, dispatch } = useGame();
  const [input, setInput] = useState('');
  const [output, setOutput] = useState(['開發者指令視窗已啟用。輸入 \'help\' 查看可用指令。']);
  const inputRef = useRef(null);
  const outputRef = useRef(null);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [output]);

  const addOutput = (text) => {
    setOutput(prev => [...prev, text]);
  };

  const handleCommand = (command) => {
    const cmd = command.trim().toLowerCase();
    addOutput(`> ${command}`);

    switch (cmd) {
      case 'help':
        addOutput('可用指令:');
        addOutput('  help - 顯示此幫助');
        addOutput('  clear - 清除輸出');
        addOutput('  godmode - 切換無敵模式');
        addOutput('  speed [數值] - 設置移動速度倍數');
        addOutput('  score [數值] - 設置分數');
        addOutput('  lives [數值] - 設置生命數');
        addOutput('  level [數值] - 設置關卡');
        addOutput('  autopilot - 切換自動駕駛模式');
        addOutput('  clever - 切換聰明模式');
        break;

      case 'clear':
        setOutput(['開發者指令視窗已啟用。輸入 \'help\' 查看可用指令。']);
        break;

      case 'godmode':
        const newGodMode = !gameState.godMode;
        dispatch({ type: GAME_ACTIONS.SET_GOD_MODE, payload: newGodMode });
        addOutput(`無敵模式: ${newGodMode ? '開啟' : '關閉'}`);
        break;

      default:
        if (cmd.startsWith('speed ')) {
          const value = parseFloat(cmd.split(' ')[1]);
          if (!isNaN(value) && value > 0) {
            dispatch({ type: GAME_ACTIONS.SET_PACMAN_SPEED_MULTIPLIER, payload: value });
            addOutput(`移動速度倍數設為: ${value}`);
          } else {
            addOutput('錯誤: 請輸入有效的數值');
          }
        } else if (cmd.startsWith('score ')) {
          const value = parseInt(cmd.split(' ')[1]);
          if (!isNaN(value) && value >= 0) {
            dispatch({ type: GAME_ACTIONS.UPDATE_SCORE, payload: value });
            addOutput(`分數設為: ${value}`);
          } else {
            addOutput('錯誤: 請輸入有效的數值');
          }
        } else if (cmd.startsWith('lives ')) {
          const value = parseInt(cmd.split(' ')[1]);
          if (!isNaN(value) && value >= 0) {
            dispatch({ type: GAME_ACTIONS.UPDATE_LIVES, payload: value });
            addOutput(`生命數設為: ${value}`);
          } else {
            addOutput('錯誤: 請輸入有效的數值');
          }
        } else if (cmd.startsWith('level ')) {
          const value = parseInt(cmd.split(' ')[1]);
          if (!isNaN(value) && value >= 1) {
            dispatch({ type: GAME_ACTIONS.UPDATE_LEVEL, payload: value });
            addOutput(`關卡設為: ${value}`);
          } else {
            addOutput('錯誤: 請輸入有效的數值');
          }
        } else if (cmd === 'autopilot') {
          const newAutoPilot = !gameState.autoPilotMode;
          dispatch({ type: GAME_ACTIONS.SET_AUTO_PILOT_MODE, payload: newAutoPilot });
          addOutput(`自動駕駛模式: ${newAutoPilot ? '開啟' : '關閉'}`);
        } else if (cmd === 'clever') {
          const newClever = !gameState.cleverMode;
          dispatch({ type: GAME_ACTIONS.SET_CLEVER_MODE, payload: newClever });
          addOutput(`聰明模式: ${newClever ? '開啟' : '關閉'}`);
        } else {
          addOutput(`未知指令: ${command}`);
        }
        break;
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (input.trim()) {
      handleCommand(input);
      setInput('');
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === '`') {
      e.preventDefault();
      dispatch({ type: GAME_ACTIONS.TOGGLE_DEV_CONSOLE });
    }
  };

  return (
    <div className="absolute bottom-0 left-0 w-full bg-black bg-opacity-85 text-green-400 font-mono text-sm p-2.5 z-[3000] border-t-2 border-green-400">
      <div
        ref={outputRef}
        className="h-25 overflow-y-auto mb-2.5 border border-green-600 p-1.5 whitespace-pre-wrap"
      >
        {output.map((line, index) => (
          <div key={index}>{line}</div>
        ))}
      </div>
      
      <form onSubmit={handleSubmit}>
        <input
          ref={inputRef}
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          className="w-full bg-gray-900 text-green-400 border border-green-600 p-1.5 font-mono text-sm focus:outline-none focus:border-green-400"
          placeholder="輸入指令..."
        />
      </form>
    </div>
  );
}

export default DevConsole;
