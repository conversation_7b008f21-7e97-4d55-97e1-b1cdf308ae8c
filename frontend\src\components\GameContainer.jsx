import { useEffect, useRef } from 'react';
import { useGame, GAME_ACTIONS } from '../contexts/GameContext';
import { useGameLogic } from '../hooks/useGameLogic';
import StartScreen from './StartScreen';
import MapSelectionScreen from './MapSelectionScreen';
import GameMap from './GameMap';
import GameUI from './GameUI';
import PauseScreen from './PauseScreen';
import GameOverScreen from './GameOverScreen';
import DevConsole from './DevConsole';
import BackgroundMap from './BackgroundMap';

function GameContainer() {
  const { gameState, dispatch } = useGame();
  const { tryStartMovement } = useGameLogic();
  const gameContainerRef = useRef(null);

  // 鍵盤控制
  useEffect(() => {
    const handleKeyDown = (e) => {
      // 開發者控制台切換
      if (e.key === '`') {
        e.preventDefault();
        dispatch({ type: GAME_ACTIONS.TOGGLE_DEV_CONSOLE });
        return;
      }

      // 如果開發者控制台開啟或在自動模式下，忽略其他按鍵
      if (gameState.isDevConsoleOpen || gameState.autoPilotMode || gameState.cleverMode) {
        return;
      }

      // 遊戲結束或無法移動時忽略移動按鍵
      if (gameState.isGameOver || !gameState.canMove || gameState.isLosingLife) {
        return;
      }

      // 暫停/繼續遊戲
      if (e.code === 'Space') {
        e.preventDefault();
        if (gameState.currentScreen === 'game') {
          dispatch({ 
            type: GAME_ACTIONS.SET_PAUSED, 
            payload: !gameState.isPaused 
          });
          dispatch({ 
            type: GAME_ACTIONS.SET_CURRENT_SCREEN, 
            payload: gameState.isPaused ? 'game' : 'pause' 
          });
        }
        return;
      }

      // 移動控制（只在遊戲進行中且未暫停時）
      if (gameState.currentScreen === 'game' && !gameState.isPaused && gameState.pacman) {
        const validMoveKeys = ['KeyW', 'KeyS', 'KeyA', 'KeyD', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
        
        if (validMoveKeys.includes(e.code)) {
          // 將方向鍵映射到 WASD
          const keyMap = {
            'ArrowUp': 'KeyW',
            'ArrowDown': 'KeyS',
            'ArrowLeft': 'KeyA',
            'ArrowRight': 'KeyD'
          };
          
          const directionKey = keyMap[e.code] || e.code;
          
          dispatch({
            type: GAME_ACTIONS.UPDATE_PACMAN_MOVEMENT,
            payload: { lastIntendedDirectionKey: directionKey }
          });

          // 如果小精靈沒有在移動，嘗試開始移動
          if (!gameState.pacmanMovement.isMoving) {
            tryStartMovement(directionKey);
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [gameState, dispatch]);

  // 根據當前螢幕狀態渲染對應組件
  const renderCurrentScreen = () => {
    switch (gameState.currentScreen) {
      case 'start':
        return <StartScreen />;
      case 'mapSelection':
        return <MapSelectionScreen />;
      case 'game':
        return (
          <>
            <GameMap />
            <GameUI />
          </>
        );
      case 'pause':
        return (
          <>
            <GameMap />
            <GameUI />
            <PauseScreen />
          </>
        );
      case 'gameOver':
        return (
          <>
            <GameMap />
            <GameUI />
            <GameOverScreen />
          </>
        );
      default:
        return <StartScreen />;
    }
  };

  return (
    <div 
      ref={gameContainerRef}
      className="relative w-screen h-screen overflow-hidden bg-black text-white font-['Press_Start_2P']"
    >
      {/* 背景地圖（只在開始畫面顯示） */}
      {gameState.currentScreen === 'start' && <BackgroundMap />}
      
      {/* 主要遊戲內容 */}
      {renderCurrentScreen()}
      
      {/* 開發者控制台 */}
      {gameState.isDevConsoleOpen && <DevConsole />}
      
      {/* WASTED 效果覆蓋層 */}
      {gameState.isLosingLife && (
        <div className="absolute inset-0 bg-black bg-opacity-0 flex items-center justify-center z-[2000] transition-all duration-[2500ms] ease-in-out">
          <div className="w-full bg-black py-5 text-center opacity-0 transform scale-100 translate-y-0">
            <span className="font-['Impact'] text-6xl text-red-600 font-bold uppercase tracking-wider shadow-text">
              WASTED
            </span>
          </div>
        </div>
      )}
      
      {/* 倒數計時 */}
      {gameState.countdown && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-5xl text-yellow-400 z-[600]">
          {gameState.countdown}
        </div>
      )}
    </div>
  );
}

export default GameContainer;
