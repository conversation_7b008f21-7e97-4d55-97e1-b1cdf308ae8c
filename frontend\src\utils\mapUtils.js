import L from 'leaflet';

// 從 Overpass API 獲取道路數據
export async function fetchRoadData(bounds) {
  const south = bounds.getSouth();
  const west = bounds.getWest();
  const north = bounds.getNorth();
  const east = bounds.getEast();
  
  const query = `[out:json][timeout:25];(way["highway"]["highway"!~"^(motorway|motorway_link|trunk|trunk_link|construction|proposed|razed|abandoned)$"]["area"!~"yes"]["access"!~"private"]["service"!~"^(driveway|parking_aisle|alley)$"](${south},${west},${north},${east}););out body;>;out skel qt;`;
  const url = `https://overpass-api.de/api/interpreter?data=${encodeURIComponent(query)}`;
  
  console.log('正在從 Overpass API 獲取道路數據...', url);
  
  try {
    const response = await fetch(url);
    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}, message: ${await response.text()}`);
      return null;
    }
    const data = await response.json();
    console.log('成功獲取到道路數據：', data.elements.length, 'elements');
    return data;
  } catch (error) {
    console.error('獲取道路數據失敗：', error);
    return null;
  }
}

// 生成道路網絡
export async function generateRoadNetwork(bounds, osmData, maxSegmentLength = 25) {
  const validPositions = [];
  const roadNetwork = [];
  const adjacencyList = new Map();
  const MAX_SEGMENT_LENGTH_METERS = maxSegmentLength;

  if (!osmData || !osmData.elements || osmData.elements.length === 0) {
    console.warn('未獲取到有效的 OSM 數據或數據為空。路網可能無法正常生成。');
    return { validPositions, roadNetwork, adjacencyList };
  }

  const nodes = {};
  const ways = [];
  
  osmData.elements.forEach(element => {
    if (element.type === 'node' && element.lat && element.lon) {
      nodes[element.id] = [element.lat, element.lon];
    } else if (element.type === 'way' && element.tags && element.tags.highway && element.nodes) {
      const roadTypes = ['motorway', 'trunk', 'primary', 'secondary', 'tertiary', 'unclassified', 'residential', 'service', 'living_street', 'pedestrian', 'road', 'path', 'footway', 'cycleway', 'track'];
      if (roadTypes.includes(element.tags.highway)) {
        ways.push(element.nodes);
      }
    }
  });

  const initialRoadNetwork = [];
  const uniqueNodesMap = new Map();

  ways.forEach(nodeIds => {
    const wayPoints = [];
    for (let i = 0; i < nodeIds.length; i++) {
      if (nodes[nodeIds[i]]) {
        const pos = nodes[nodeIds[i]];
        wayPoints.push(pos);
        if (!uniqueNodesMap.has(pos.toString())) {
          uniqueNodesMap.set(pos.toString(), pos);
        }
      }
    }
    if (wayPoints.length > 1) {
      for (let i = 0; i < wayPoints.length - 1; i++) {
        initialRoadNetwork.push([wayPoints[i], wayPoints[i + 1]]);
      }
    }
  });

  // 細分長路段
  const subdividedRoadNetwork = [];
  initialRoadNetwork.forEach(segment => {
    const nodeA = segment[0];
    const nodeB = segment[1];

    if (!uniqueNodesMap.has(nodeA.toString())) uniqueNodesMap.set(nodeA.toString(), nodeA);
    if (!uniqueNodesMap.has(nodeB.toString())) uniqueNodesMap.set(nodeB.toString(), nodeB);

    const latLngA = L.latLng(nodeA[0], nodeA[1]);
    const latLngB = L.latLng(nodeB[0], nodeB[1]);
    const distance = latLngA.distanceTo(latLngB);

    if (distance > MAX_SEGMENT_LENGTH_METERS) {
      const numNewPoints = Math.ceil(distance / MAX_SEGMENT_LENGTH_METERS) - 1;
      let lastPoint = nodeA;
      for (let i = 1; i <= numNewPoints; i++) {
        const fraction = i / (numNewPoints + 1);
        const interpolatedLat = nodeA[0] + fraction * (nodeB[0] - nodeA[0]);
        const interpolatedLng = nodeA[1] + fraction * (nodeB[1] - nodeA[1]);
        const newNode = [interpolatedLat, interpolatedLng];

        if (!uniqueNodesMap.has(newNode.toString())) {
          uniqueNodesMap.set(newNode.toString(), newNode);
        }
        subdividedRoadNetwork.push([lastPoint, newNode]);
        lastPoint = newNode;
      }
      subdividedRoadNetwork.push([lastPoint, nodeB]);
    } else {
      subdividedRoadNetwork.push(segment);
    }
  });

  // 設置結果
  roadNetwork.push(...subdividedRoadNetwork);
  validPositions.push(...Array.from(uniqueNodesMap.values()));

  // 建立鄰接列表
  roadNetwork.forEach(segment => {
    if (segment && segment.length === 2 && segment[0] && segment[1]) {
      const nodeA = segment[0];
      const nodeB = segment[1];
      const nodeAStr = nodeA.toString();
      const nodeBStr = nodeB.toString();

      if (!adjacencyList.has(nodeAStr)) adjacencyList.set(nodeAStr, []);
      if (!adjacencyList.has(nodeBStr)) adjacencyList.set(nodeBStr, []);

      if (!adjacencyList.get(nodeAStr).some(n => positionsAreEqual(n, nodeB))) {
        adjacencyList.get(nodeAStr).push(nodeB);
      }
      if (!adjacencyList.get(nodeBStr).some(n => positionsAreEqual(n, nodeA))) {
        adjacencyList.get(nodeBStr).push(nodeA);
      }
    }
  });

  return { validPositions, roadNetwork, adjacencyList };
}

// 尋找最近的道路位置
export function findNearestRoadPosition(targetLat, targetLng, validPositionsList) {
  if (!validPositionsList || validPositionsList.length === 0) {
    console.warn("findNearestRoadPosition with no valid positions.");
    return [targetLat, targetLng];
  }
  
  let nearestPos = validPositionsList[0];
  let minDistanceSq = Infinity;
  
  for (const pos of validPositionsList) {
    const dy = pos[0] - targetLat;
    const dx = pos[1] - targetLng;
    const distanceSq = dy * dy + dx * dx;
    if (distanceSq < minDistanceSq) {
      minDistanceSq = distanceSq;
      nearestPos = pos;
    }
  }
  
  return nearestPos;
}

// 繪製視覺化道路
export function drawVisualRoads(map, roadNetwork) {
  const roadLayers = [];
  
  roadNetwork.forEach(segment => {
    if (segment && segment.length > 1 && map) {
      const blueBorder = L.polyline(segment, { color: 'blue', weight: 14, opacity: 0.8 });
      roadLayers.push(blueBorder);
      map.addLayer(blueBorder);
      
      const blackRoad = L.polyline(segment, { color: 'black', weight: 10, opacity: 1 });
      roadLayers.push(blackRoad);
      map.addLayer(blackRoad);
      
      blueBorder.bringToBack();
      blackRoad.bringToFront();
    }
  });
  
  return roadLayers;
}

// 位置比較函數
export function positionsAreEqual(p1, p2, tolerance = 0.000001) {
  if (!p1 || !p2 || p1.length !== 2 || p2.length !== 2) return false;
  return Math.abs(p1[0] - p2[0]) < tolerance && Math.abs(p1[1] - p2[1]) < tolerance;
}

// 連接死路
export function connectDeadEnds(validPositions, roadNetwork, adjacencyList) {
  // 這是一個簡化版本，完整實現會更複雜
  // 暫時返回原始數據
  return { validPositions, roadNetwork, adjacencyList };
}

// 移除斷開的島嶼
export function removeDisconnectedIslands(validPositions, roadNetwork, adjacencyList) {
  // 這是一個簡化版本，完整實現會更複雜
  // 暫時返回原始數據
  return { validPositions, roadNetwork, adjacencyList };
}
