import { GameProvider } from './contexts/GameContext';
import { <PERSON><PERSON>ontaine<PERSON>, TileLayer } from 'react-leaflet';

// 簡化的 StartScreen 組件
function SimpleStartScreen() {
  return (
    <div className="absolute inset-0 flex flex-col justify-center items-center p-5 text-center bg-black bg-opacity-50 z-[1000]">
      <h1 className="text-4xl md:text-6xl text-yellow-400 mb-6">
        PAC-MAP
      </h1>
      <div className="flex flex-col space-y-4">
        <button className="px-6 py-3 bg-blue-600 text-white rounded hover:bg-blue-700">
          開始遊戲
        </button>
        <button className="px-6 py-3 bg-green-600 text-white rounded hover:bg-green-700">
          遊戲說明
        </button>
        <button className="px-6 py-3 bg-purple-600 text-white rounded hover:bg-purple-700">
          排行榜
        </button>
      </div>
    </div>
  );
}

function App() {
  try {
    return (
      <GameProvider>
        <div className="relative w-screen h-screen bg-black text-white">
          {/* 背景地圖 */}
          <div className="absolute inset-0 w-full h-full z-[998] bg-gray-800 opacity-30">
            <MapContainer
              center={[25.0330, 121.5654]}
              zoom={18}
              zoomControl={false}
              attributionControl={false}
              scrollWheelZoom={false}
              dragging={false}
              className="w-full h-full"
            >
              <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
            </MapContainer>
          </div>

          <SimpleStartScreen />
        </div>
      </GameProvider>
    );
  } catch (error) {
    console.error('App error:', error);
    return (
      <div className="w-screen h-screen bg-red-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl mb-4">載入錯誤</h1>
          <p>{error.message}</p>
        </div>
      </div>
    );
  }
}

export default App;
