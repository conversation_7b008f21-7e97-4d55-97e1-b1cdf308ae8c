import { GameProvider } from './contexts/GameContext';
import GameContainer from './components/GameContainer';

function App() {
  try {
    return (
      <GameProvider>
        <GameContainer />
      </GameProvider>
    );
  } catch (error) {
    console.error('App render error:', error);
    return (
      <div className="w-screen h-screen bg-red-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl mb-4">載入錯誤</h1>
          <p>{error.message}</p>
        </div>
      </div>
    );
  }
}

export default App;
