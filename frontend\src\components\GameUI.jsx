import { useEffect, useState } from 'react';
import { useGame } from '../contexts/GameContext';

function GameUI() {
  const { gameState } = useGame();
  const [formattedTime, setFormattedTime] = useState('5:00');

  // 格式化時間顯示
  useEffect(() => {
    const minutes = Math.floor(gameState.gameTime / 60);
    const seconds = gameState.gameTime % 60;
    setFormattedTime(`${minutes}:${seconds.toString().padStart(2, '0')}`);
  }, [gameState.gameTime]);

  // 計算剩餘點數
  const dotsLeft = gameState.totalDots - gameState.dotsCollected;

  // 獲取最高分
  const highScore = gameState.leaderboard.length > 0 
    ? Math.max(...gameState.leaderboard.map(entry => entry.score))
    : 0;

  return (
    <div className="absolute top-5 left-5 z-[500] bg-black bg-opacity-80 text-white p-4 rounded-lg font-['Press_Start_2P'] text-sm border border-gray-500">
      {/* 第一行：分數和生命 */}
      <div className="flex justify-between mb-1 min-w-[300px]">
        <span>
          分數: <span className="text-yellow-400">{gameState.score}</span>
        </span>
        <span>
          生命: <span className="text-red-400">{gameState.lives}</span>
        </span>
      </div>

      {/* 第二行：關卡和時間 */}
      <div className="flex justify-between mb-1">
        <span>
          關卡: <span className="text-green-400">{gameState.level}</span>
        </span>
        <span>
          時間: <span className="text-blue-400">{formattedTime}</span>
        </span>
      </div>

      {/* 第三行：剩餘點數和最高分 */}
      <div className="flex justify-between">
        <span>
          剩餘點數: <span className="text-yellow-400">{dotsLeft}</span>
        </span>
        <span>
          最高分: <span className="text-purple-400">{highScore}</span>
        </span>
      </div>

      {/* 能量模式指示器 */}
      {gameState.powerMode && (
        <div className="mt-2 text-center">
          <span className="text-yellow-400 animate-pulse">
            ⚡ 能量模式 ⚡
          </span>
        </div>
      )}

      {/* 暫停指示器 */}
      {gameState.isPaused && (
        <div className="mt-2 text-center">
          <span className="text-red-400 animate-pulse">
            ⏸️ 遊戲暫停
          </span>
        </div>
      )}
    </div>
  );
}

export default GameUI;
