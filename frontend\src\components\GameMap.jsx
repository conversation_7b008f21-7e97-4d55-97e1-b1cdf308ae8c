import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer } from 'react-leaflet';
import { useGame, MAP_CONFIGS } from '../contexts/GameContext';
import 'leaflet/dist/leaflet.css';

function GameMap() {
  const { gameState } = useGame();
  const mapRef = useRef(null);

  const currentMapConfig = MAP_CONFIGS[gameState.currentMapIndex];

  useEffect(() => {
    // 這裡會初始化遊戲邏輯
    // 暫時先渲染基本地圖
    console.log('GameMap mounted, initializing game...');
  }, [gameState.currentMapIndex]);

  return (
    <div className="w-full h-full z-[1] bg-gray-900">
      <MapContainer
        ref={mapRef}
        center={currentMapConfig.center}
        zoom={currentMapConfig.zoom}
        minZoom={currentMapConfig.zoom}
        maxZoom={currentMapConfig.zoom}
        zoomControl={false}
        attributionControl={false}
        scrollWheelZoom={false}
        dragging={false}
        touchZoom={false}
        doubleClickZoom={false}
        className="w-full h-full brightness-150 transition-all duration-[2500ms] ease-in-out"
        style={{ filter: gameState.isLosingLife ? 'brightness(0%) grayscale(100%)' : 'brightness(150%)' }}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          maxZoom={currentMapConfig.zoom + 1}
        />
      </MapContainer>
    </div>
  );
}

export default GameMap;
