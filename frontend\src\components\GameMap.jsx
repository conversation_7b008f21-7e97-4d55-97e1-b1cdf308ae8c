import { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Tile<PERSON>ayer, useMap } from 'react-leaflet';
import { useGame, MAP_CONFIGS, GAME_CONSTANTS } from '../contexts/GameContext';
import { useGameLogic } from '../hooks/useGameLogic';
import 'leaflet/dist/leaflet.css';

// 內部組件來獲取地圖實例
function MapInitializer() {
  const map = useMap();
  const { gameState } = useGame();
  const { initGame, startGameLoop } = useGameLogic();
  const initializedRef = useRef(false);

  useEffect(() => {
    if (map && !initializedRef.current) {
      initializedRef.current = true;

      // 設置地圖配置
      const config = MAP_CONFIGS[gameState.currentMapIndex];
      map.setView(config.center, config.zoom);
      map.setMinZoom(config.zoom);
      map.setMaxZoom(config.zoom);

      // 初始化遊戲
      const initializeGame = async () => {
        await initGame(map);
        startGameLoop();
      };

      // 延遲初始化以確保地圖完全載入
      setTimeout(initializeGame, 1000);
    }
  }, [map, gameState.currentMapIndex, initGame, startGameLoop]);

  return null;
}

function GameMap() {
  const { gameState } = useGame();
  const mapRef = useRef(null);

  const currentMapConfig = MAP_CONFIGS[gameState.currentMapIndex];

  return (
    <div className="w-full h-full z-[1] bg-gray-900">
      <MapContainer
        ref={mapRef}
        center={currentMapConfig.center}
        zoom={currentMapConfig.zoom}
        minZoom={currentMapConfig.zoom}
        maxZoom={currentMapConfig.zoom}
        zoomControl={false}
        attributionControl={false}
        scrollWheelZoom={false}
        dragging={false}
        touchZoom={false}
        doubleClickZoom={false}
        className="w-full h-full brightness-150 transition-all duration-[2500ms] ease-in-out"
        style={{ filter: gameState.isLosingLife ? 'brightness(0%) grayscale(100%)' : 'brightness(150%)' }}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          maxZoom={currentMapConfig.zoom + 1}
        />
        <MapInitializer />
      </MapContainer>
    </div>
  );
}

export default GameMap;
