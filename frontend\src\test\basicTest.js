// 基本功能測試
// 這個檔案可以用來測試遊戲的基本功能

export function testGameState() {
  console.log('Testing game state...');
  
  // 測試遊戲狀態初始化
  const initialState = {
    score: 0,
    lives: 3,
    level: 1,
    gameTime: 600,
    isPaused: false,
    isGameOver: false,
    currentMapIndex: 0,
    currentScreen: 'start'
  };
  
  console.log('Initial state:', initialState);
  return true;
}

export function testMapUtils() {
  console.log('Testing map utilities...');
  
  // 測試位置比較函數
  const pos1 = [25.0330, 121.5654];
  const pos2 = [25.0330, 121.5654];
  const pos3 = [25.0331, 121.5655];
  
  // 這裡會需要導入 positionsAreEqual 函數
  // const { positionsAreEqual } = require('../utils/mapUtils');
  // console.log('Positions equal test:', positionsAreEqual(pos1, pos2)); // should be true
  // console.log('Positions not equal test:', positionsAreEqual(pos1, pos3)); // should be false
  
  console.log('Map utils test completed');
  return true;
}

export function testAIUtils() {
  console.log('Testing AI utilities...');
  
  // 測試方向計算
  const from = [25.0330, 121.5654];
  const to = [25.0331, 121.5654]; // 向北移動
  
  // 這裡會需要導入 calculateDirection 函數
  // const { calculateDirection } = require('../utils/aiUtils');
  // console.log('Direction test:', calculateDirection(from, to)); // should be 'up'
  
  console.log('AI utils test completed');
  return true;
}

export function runAllTests() {
  console.log('Running all basic tests...');
  
  const results = {
    gameState: testGameState(),
    mapUtils: testMapUtils(),
    aiUtils: testAIUtils()
  };
  
  console.log('Test results:', results);
  
  const allPassed = Object.values(results).every(result => result === true);
  console.log('All tests passed:', allPassed);
  
  return allPassed;
}

// 如果在瀏覽器環境中，將測試函數添加到 window 對象
if (typeof window !== 'undefined') {
  window.gameTests = {
    testGameState,
    testMapUtils,
    testAIUtils,
    runAllTests
  };
}
