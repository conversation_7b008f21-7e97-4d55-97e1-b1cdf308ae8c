/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'pixel': ['"Press Start 2P"', 'cursive'],
      },
      textShadow: {
        'glow': '0 0 5px #fff, 0 0 10px #fff, 0 0 15px #ffff00, 0 0 20px #ffff00, 0 0 25px #ffff00, 0 0 30px #ff0000, 0 0 35px #ff0000',
        'black': '2px 2px 0px #000',
        'red': '3px 3px 0px #000000, 0 0 8px #fff, 0 0 12px #FF0000, 0 0 18px #FFB84D',
      },
      borderWidth: {
        '3': '3px',
        '4': '4px',
      },
      animation: {
        'title-pulse': 'gameTitlePulse 2s infinite ease-in-out',
        'blink-red': 'blinkRed 0.8s infinite steps(1, end)',
      },
      keyframes: {
        gameTitlePulse: {
          '0%, 100%': { 
            transform: 'scale(1)', 
            opacity: '1' 
          },
          '50%': { 
            transform: 'scale(1.05)', 
            opacity: '0.85' 
          }
        },
        blinkRed: {
          '0%, 100%': { 
            opacity: '1', 
            transform: 'scale(1)' 
          },
          '50%': { 
            opacity: '0.6', 
            transform: 'scale(1.02)' 
          }
        }
      }
    },
  },
  plugins: [
    function({ addUtilities }) {
      const newUtilities = {
        '.text-shadow-glow': {
          textShadow: '0 0 5px #fff, 0 0 10px #fff, 0 0 15px #ffff00, 0 0 20px #ffff00, 0 0 25px #ffff00, 0 0 30px #ff0000, 0 0 35px #ff0000',
        },
        '.text-shadow-black': {
          textShadow: '2px 2px 0px #000',
        },
        '.text-shadow-red': {
          textShadow: '3px 3px 0px #000000, 0 0 8px #fff, 0 0 12px #FF0000, 0 0 18px #FFB84D',
        },
      }
      addUtilities(newUtilities)
    }
  ],
}
