@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定義 CSS 樣式 */
.pacman-pixel-button {
  font-family: "Press Start 2P", cursive;
  background-color: #2121de;
  color: #ffff00;
  border: 3px solid #000000;
  padding: 12px 24px;
  font-size: clamp(0.9rem, 3vw, 1.2rem);
  text-transform: uppercase;
  border-radius: 0;
  box-shadow: 4px 4px 0px rgba(0, 0, 0, 0.75);
  cursor: pointer;
  transition: transform 0.05s ease-out, box-shadow 0.05s ease-out,
    background-color 0.05s ease-out;
  margin: 10px;
  display: inline-block;
  outline: none;
  min-width: 180px;
}

.pacman-pixel-button:hover {
  background-color: #4242ff;
  color: #ffff66;
  box-shadow: 2px 2px 0px rgba(0, 0, 0, 0.75);
  transform: translate(2px, 2px);
}

.pacman-pixel-button:active {
  background-color: #0000b3;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.75);
  transform: translate(4px, 4px);
}

/* 遊戲標題動畫 */
@keyframes gameTitlePulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.85;
  }
}

/* 暫停畫面標題閃爍 */
@keyframes blinkRed {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02);
  }
}

/* 小精靈圖標 */
.pacman-icon {
  width: 24px;
  height: 24px;
  background-color: #ffff00;
  border-radius: 50%;
  position: relative;
  transition: transform 0.05s linear, opacity 0.3s ease-out;
}

.pacman-icon.hidden {
  opacity: 0;
  pointer-events: none;
}

.pacman-icon::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
  border-style: solid;
  border-color: transparent #111 transparent transparent;
  animation: pacman-mouth-chomp 0.4s infinite;
}

@keyframes pacman-mouth-chomp {
  0%,
  100% {
    border-width: 12px 12px 12px 0;
  }
  50% {
    border-width: 4px 12px 4px 0;
  }
}

.pacman-icon.facing-true-left {
  transform: rotate(180deg);
}
.pacman-icon.facing-true-right {
  transform: rotate(0deg);
}
.pacman-icon.facing-true-up {
  transform: rotate(-90deg);
}
.pacman-icon.facing-true-down {
  transform: rotate(90deg);
}

/* 鬼怪圖標 */
.ghost-icon {
  width: 20px;
  height: 20px;
  border-radius: 10px 10px 0 0;
  position: relative;
  overflow: visible;
}

.ghost-icon::before,
.ghost-icon::after {
  content: "";
  position: absolute;
  width: 6px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
  top: 4px;
  border: 1px solid #555;
}

.ghost-icon::before {
  left: 3px;
}
.ghost-icon::after {
  right: 3px;
}

.ghost-icon > div.wave1,
.ghost-icon > div.wave2,
.ghost-icon > div.wave3 {
  position: absolute;
  bottom: -6px;
  width: 33.33%;
  height: 6px;
  background-color: inherit;
  border-radius: 0 0 50% 50% / 0 0 100% 100%;
}

.ghost-icon > div.wave1 {
  left: 0;
}
.ghost-icon > div.wave2 {
  left: 33.33%;
}
.ghost-icon > div.wave3 {
  left: 66.66%;
}

/* 鬼怪顏色 */
.ghost-red {
  background: #ff0000;
}
.ghost-pink {
  background: #ffc0cb;
}
.ghost-cyan {
  background: #00ffff;
}
.ghost-orange {
  background: #ffb84d;
}
.ghost-purple {
  background: #800080;
}
.ghost-green {
  background: #008000;
}
.ghost-blue {
  background: #0000ff;
}
.ghost-scared {
  background: #2222dd;
}

.ghost-scared::before,
.ghost-scared::after {
  background-color: white;
  width: 8px;
  height: 4px;
  top: 7px;
}

/* 點數和大力丸 */
.dot {
  width: 4px;
  height: 4px;
  background: #ffff00;
  border-radius: 50%;
}

.power-pellet {
  width: 12px;
  height: 12px;
  background: #ffff00;
  border-radius: 50%;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0.3;
  }
}

/* 文字陰影效果 */
.text-shadow-glow {
  text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #ffff00, 0 0 20px #ffff00,
    0 0 25px #ffff00, 0 0 30px #ff0000, 0 0 35px #ff0000;
}

.shadow-text {
  text-shadow: 2px 2px 0 #000, -1px -1px 0 #000, 1px -1px 0 #000,
    -1px 1px 0 #000, 1px 1px 0 #000, 3px 3px 5px rgba(0, 0, 0, 0.5);
}

/* 確保字體載入 */
body {
  font-family: "Press Start 2P", cursive, system-ui, -apple-system, sans-serif;
}

/* 自定義 TailwindCSS 工具類別 */
.text-shadow-glow {
  text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #ffff00, 0 0 20px #ffff00, 0 0 25px #ffff00, 0 0 30px #ff0000, 0 0 35px #ff0000;
}

.text-shadow-black {
  text-shadow: 2px 2px 0px #000;
}

.text-shadow-red {
  text-shadow: 3px 3px 0px #000000, 0 0 8px #fff, 0 0 12px #FF0000, 0 0 18px #FFB84D;
}

.border-3 {
  border-width: 3px;
}

.border-4 {
  border-width: 4px;
}

/* 動畫類別 */
.animate-title-pulse {
  animation: gameTitlePulse 2s infinite ease-in-out;
}

.animate-blink-red {
  animation: blinkRed 0.8s infinite steps(1, end);
}
