import React from 'react';
import { useGame, GAME_ACTIONS, MAP_CONFIGS } from '../contexts/GameContext';

function MapSelectionScreen() {
  const { gameState, dispatch } = useGame();

  const handleMapSelect = async (mapIndex) => {
    // 設置選中的地圖
    dispatch({ type: GAME_ACTIONS.SET_CURRENT_MAP_INDEX, payload: mapIndex });
    
    // 切換到遊戲畫面
    dispatch({ type: GAME_ACTIONS.SET_CURRENT_SCREEN, payload: 'game' });
    
    // 啟動音訊上下文（如果需要）
    if (typeof Tone !== 'undefined' && Tone.context.state !== 'running') {
      try {
        await Tone.start();
        console.log("音訊內容已由使用者互動啟動 (地圖選擇)");
      } catch (error) {
        console.warn("無法啟動音訊上下文:", error);
      }
    }
    
    // 這裡會觸發遊戲初始化
    // 實際的遊戲初始化邏輯會在 GameMap 組件中處理
  };

  const handleBackToStart = () => {
    dispatch({ type: GAME_ACTIONS.SET_CURRENT_SCREEN, payload: 'start' });
    dispatch({ type: GAME_ACTIONS.TOGGLE_INSTRUCTIONS, payload: false });
    dispatch({ type: GAME_ACTIONS.TOGGLE_LEADERBOARD, payload: false });
  };

  return (
    <div className="absolute inset-0 flex flex-col justify-center items-center p-5 text-center overflow-y-auto bg-black bg-opacity-85 z-[1001]">
      {/* 標題 */}
      <h2 className="text-3xl md:text-5xl lg:text-6xl text-yellow-400 mb-8 text-shadow-[2px_2px_0px_#ff0000]">
        選擇地圖
      </h2>

      {/* 地圖選擇按鈕 */}
      <div className="flex flex-wrap justify-center items-center mb-8">
        {MAP_CONFIGS.map((config, index) => (
          <button
            key={index}
            onClick={() => handleMapSelect(index)}
            className={`pacman-pixel-button m-2 ${
              gameState.currentMapIndex === index 
                ? 'bg-yellow-400 text-blue-800 border-black shadow-[2px_2px_0px_rgba(0,0,0,0.75)] transform translate-x-0.5 translate-y-0.5' 
                : ''
            }`}
          >
            {config.name}
          </button>
        ))}
      </div>

      {/* 返回按鈕 */}
      <button
        onClick={handleBackToStart}
        className="pacman-pixel-button"
      >
        返回主選單
      </button>
    </div>
  );
}

export default MapSelectionScreen;
