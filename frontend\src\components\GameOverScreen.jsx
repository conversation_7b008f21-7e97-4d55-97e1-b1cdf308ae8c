import React, { useEffect, useState } from 'react';
import { useGame, GAME_ACTIONS } from '../contexts/GameContext';

function GameOverScreen() {
  const { gameState, dispatch } = useGame();
  const [isNewHighScore, setIsNewHighScore] = useState(false);
  const [gameOverTitle, setGameOverTitle] = useState('遊戲結束');

  useEffect(() => {
    // 檢查是否為新紀錄
    const currentHighScore = gameState.leaderboard.length > 0 
      ? Math.max(...gameState.leaderboard.map(entry => entry.score))
      : 0;
    
    setIsNewHighScore(gameState.score > currentHighScore);

    // 根據遊戲結束原因設置標題
    if (gameState.lives <= 0) {
      setGameOverTitle('遊戲結束');
    } else if (gameState.gameTime <= 0) {
      setGameOverTitle('時間到！');
    } else if (gameState.dotsCollected >= gameState.totalDots) {
      setGameOverTitle('恭喜過關！');
    } else {
      setGameOverTitle('遊戲結束');
    }
  }, [gameState.score, gameState.leaderboard, gameState.lives, gameState.gameTime, gameState.dotsCollected, gameState.totalDots]);

  const handleRestartGame = () => {
    dispatch({ type: GAME_ACTIONS.RESET_GAME });
    dispatch({ type: GAME_ACTIONS.SET_CURRENT_SCREEN, payload: 'mapSelection' });
  };

  const handleBackToMenu = () => {
    dispatch({ type: GAME_ACTIONS.RESET_GAME });
    dispatch({ type: GAME_ACTIONS.SET_CURRENT_SCREEN, payload: 'start' });
  };

  const handleSaveScore = () => {
    const playerName = prompt('請輸入您的名字：');
    if (playerName && playerName.trim()) {
      const newEntry = {
        name: playerName.trim(),
        score: gameState.score,
        level: gameState.level,
        date: new Date().toLocaleDateString('zh-TW')
      };

      const updatedLeaderboard = [...gameState.leaderboard, newEntry]
        .sort((a, b) => b.score - a.score)
        .slice(0, 10); // 只保留前10名

      dispatch({
        type: GAME_ACTIONS.UPDATE_LEADERBOARD,
        payload: updatedLeaderboard
      });

      setIsNewHighScore(false);
    }
  };

  return (
    <div className="absolute inset-0 flex flex-col justify-center items-center p-5 text-center bg-black bg-opacity-90 z-[2500] text-white">
      {/* 遊戲結束標題 */}
      <h2 className="text-4xl md:text-6xl lg:text-7xl text-red-500 mb-8">
        <span className="text-shadow-[3px_3px_0px_#000000,0_0_8px_#fff,0_0_12px_#FF0000]">
          {gameOverTitle}
        </span>
      </h2>

      {/* 最終分數 */}
      <div className="flex justify-center mb-5">
        <span className="text-2xl">
          最終分數: <span className="text-yellow-400">{gameState.score}</span>
        </span>
      </div>

      {/* 新紀錄提示 */}
      {isNewHighScore && (
        <div className="text-yellow-400 text-xl mb-5 animate-pulse">
          🏆 新紀錄！
        </div>
      )}

      {/* 遊戲統計 */}
      <div className="mb-8 text-sm space-y-2">
        <div>關卡: {gameState.level}</div>
        <div>收集點數: {gameState.dotsCollected} / {gameState.totalDots}</div>
        <div>剩餘生命: {gameState.lives}</div>
        <div>剩餘時間: {Math.floor(gameState.gameTime / 60)}:{(gameState.gameTime % 60).toString().padStart(2, '0')}</div>
      </div>

      {/* 按鈕組 */}
      <div className="flex flex-col items-center space-y-4">
        {isNewHighScore && (
          <button
            onClick={handleSaveScore}
            className="pacman-pixel-button bg-yellow-400 text-black hover:bg-yellow-300"
          >
            儲存分數
          </button>
        )}
        
        <button
          onClick={handleRestartGame}
          className="pacman-pixel-button"
        >
          重新開始
        </button>
        
        <button
          onClick={handleBackToMenu}
          className="pacman-pixel-button"
        >
          回到主選單
        </button>
      </div>
    </div>
  );
}

export default GameOverScreen;
