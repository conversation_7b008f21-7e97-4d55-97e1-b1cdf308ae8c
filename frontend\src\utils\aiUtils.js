import L from 'leaflet';
import { positionsAreEqual } from './mapUtils';

// 啟發式函數（用於 A* 算法）
function heuristic(nodeA, nodeB) {
  if (!nodeA || !nodeB) return Infinity;
  return L.latLng(nodeA[0], nodeA[1]).distanceTo(L.latLng(nodeB[0], nodeB[1]));
}

// 獲取鄰居節點
export function getNeighbors(node, adjacencyList) {
  const nodeStr = node.toString();
  return adjacencyList.has(nodeStr) ? adjacencyList.get(nodeStr) : [];
}

// A* 路徑搜尋算法
export function aStarSearch(startNode, goalNode, validPositions, adjacencyList) {
  if (!startNode || !goalNode || !validPositions || !adjacencyList) {
    return [];
  }

  if (positionsAreEqual(startNode, goalNode)) {
    return [startNode];
  }

  const openSet = [startNode];
  const cameFrom = new Map();
  const gScore = new Map();
  const fScore = new Map();

  // 初始化所有節點的分數
  validPositions.forEach(pos => {
    gScore.set(pos.toString(), Infinity);
    fScore.set(pos.toString(), Infinity);
  });

  gScore.set(startNode.toString(), 0);
  fScore.set(startNode.toString(), heuristic(startNode, goalNode));

  while (openSet.length > 0) {
    // 找到 fScore 最小的節點
    let current = openSet[0];
    let currentIndex = 0;
    for (let i = 1; i < openSet.length; i++) {
      if (fScore.get(openSet[i].toString()) < fScore.get(current.toString())) {
        current = openSet[i];
        currentIndex = i;
      }
    }

    if (positionsAreEqual(current, goalNode)) {
      // 重建路徑
      const path = [];
      let temp = current;
      while (temp) {
        path.unshift(temp);
        temp = cameFrom.get(temp.toString());
      }
      return path;
    }

    openSet.splice(currentIndex, 1);
    const neighbors = getNeighbors(current, adjacencyList);

    for (const neighbor of neighbors) {
      const costToNeighbor = heuristic(current, neighbor);
      const tentativeGScore = gScore.get(current.toString()) + costToNeighbor;

      if (tentativeGScore < gScore.get(neighbor.toString())) {
        cameFrom.set(neighbor.toString(), current);
        gScore.set(neighbor.toString(), tentativeGScore);
        fScore.set(neighbor.toString(), tentativeGScore + heuristic(neighbor, goalNode));
        
        if (!openSet.find(node => positionsAreEqual(node, neighbor))) {
          openSet.push(neighbor);
        }
      }
    }
  }

  return []; // 沒有找到路徑
}

// 鬼怪 AI 決策
export function decideGhostMove(ghost, pacmanPosition, validPositions, adjacencyList, powerMode = false) {
  if (!ghost || !pacmanPosition || !validPositions || !adjacencyList) {
    return null;
  }

  const ghostPosition = [ghost.getLatLng().lat, ghost.getLatLng().lng];
  const neighbors = getNeighbors(ghostPosition, adjacencyList);

  if (neighbors.length === 0) {
    return null;
  }

  if (powerMode) {
    // 能量模式下，鬼怪逃離小精靈
    let bestMove = neighbors[0];
    let maxDistance = heuristic(bestMove, pacmanPosition);

    for (const neighbor of neighbors) {
      const distance = heuristic(neighbor, pacmanPosition);
      if (distance > maxDistance) {
        maxDistance = distance;
        bestMove = neighbor;
      }
    }

    return bestMove;
  } else {
    // 正常模式下，鬼怪追逐小精靈
    const path = aStarSearch(ghostPosition, pacmanPosition, validPositions, adjacencyList);
    
    if (path.length > 1) {
      return path[1]; // 返回路徑中的下一個節點
    } else {
      // 如果沒有路徑，隨機選擇一個鄰居
      return neighbors[Math.floor(Math.random() * neighbors.length)];
    }
  }
}

// 自動駕駛模式的路徑規劃
export function planAutoPilotPath(currentPosition, dots, validPositions, adjacencyList) {
  if (!currentPosition || !dots || dots.length === 0 || !validPositions || !adjacencyList) {
    return [];
  }

  // 找到最近的點數
  let nearestDot = null;
  let minDistance = Infinity;

  for (const dot of dots) {
    const dotPosition = [dot.getLatLng().lat, dot.getLatLng().lng];
    const distance = heuristic(currentPosition, dotPosition);
    if (distance < minDistance) {
      minDistance = distance;
      nearestDot = dotPosition;
    }
  }

  if (nearestDot) {
    return aStarSearch(currentPosition, nearestDot, validPositions, adjacencyList);
  }

  return [];
}

// 聰明模式的路徑規劃（更高效的點數收集）
export function planCleverPath(currentPosition, dots, powerPellets, validPositions, adjacencyList) {
  if (!currentPosition || !validPositions || !adjacencyList) {
    return [];
  }

  // 優先考慮大力丸
  if (powerPellets && powerPellets.length > 0) {
    let nearestPowerPellet = null;
    let minDistance = Infinity;

    for (const pellet of powerPellets) {
      const pelletPosition = [pellet.getLatLng().lat, pellet.getLatLng().lng];
      const distance = heuristic(currentPosition, pelletPosition);
      if (distance < minDistance) {
        minDistance = distance;
        nearestPowerPellet = pelletPosition;
      }
    }

    if (nearestPowerPellet) {
      return aStarSearch(currentPosition, nearestPowerPellet, validPositions, adjacencyList);
    }
  }

  // 如果沒有大力丸，找最近的點數
  if (dots && dots.length > 0) {
    return planAutoPilotPath(currentPosition, dots, validPositions, adjacencyList);
  }

  return [];
}

// 檢查碰撞
export function checkCollision(position1, position2, tolerance = 0.0001) {
  return positionsAreEqual(position1, position2, tolerance);
}

// 計算移動方向
export function calculateDirection(from, to) {
  if (!from || !to) return 'right';

  const deltaLat = to[0] - from[0];
  const deltaLng = to[1] - from[1];

  if (Math.abs(deltaLat) > Math.abs(deltaLng)) {
    return deltaLat > 0 ? 'up' : 'down';
  } else {
    return deltaLng > 0 ? 'right' : 'left';
  }
}

// 生成隨機的鬼怪出生點
export function generateGhostSpawnPoints(validPositions, count = 7) {
  if (!validPositions || validPositions.length === 0) {
    return [];
  }

  const spawnPoints = [];
  const usedIndices = new Set();

  for (let i = 0; i < count && spawnPoints.length < validPositions.length; i++) {
    let randomIndex;
    do {
      randomIndex = Math.floor(Math.random() * validPositions.length);
    } while (usedIndices.has(randomIndex));

    usedIndices.add(randomIndex);
    spawnPoints.push(validPositions[randomIndex]);
  }

  return spawnPoints;
}
