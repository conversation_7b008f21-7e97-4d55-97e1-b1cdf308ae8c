import { useGame, GAME_ACTIONS } from '../contexts/GameContext';

function StartScreen() {
  const { gameState, dispatch } = useGame();

  const handleStartGame = () => {
    dispatch({ type: GAME_ACTIONS.SET_CURRENT_SCREEN, payload: 'mapSelection' });
    dispatch({ type: GAME_ACTIONS.TOGGLE_INSTRUCTIONS, payload: false });
    dispatch({ type: GAME_ACTIONS.TOGGLE_LEADERBOARD, payload: false });
  };

  const handleToggleInstructions = () => {
    dispatch({ type: GAME_ACTIONS.TOGGLE_INSTRUCTIONS });
  };

  const handleToggleLeaderboard = () => {
    dispatch({ type: GAME_ACTIONS.TOGGLE_LEADERBOARD });
  };

  return (
    <div className="absolute inset-0 flex flex-col justify-center items-center p-5 text-center overflow-y-auto bg-black bg-opacity-30 z-[1000]">
      {/* 遊戲標題 */}
      <h1 className="text-4xl md:text-6xl lg:text-7xl text-yellow-400 mb-6 animate-title-pulse tracking-wider">
        <span className="text-shadow-glow">
          PAC-MAP
        </span>
      </h1>

      {/* 主選單按鈕 */}
      <div className="flex justify-center items-center flex-wrap mb-4">
        <button
          onClick={handleStartGame}
          className="pacman-pixel-button m-2.5"
        >
          開始遊戲
        </button>
        <button
          onClick={handleToggleInstructions}
          className="pacman-pixel-button m-2.5"
        >
          遊戲說明
        </button>
        <button
          onClick={handleToggleLeaderboard}
          className="pacman-pixel-button m-2.5"
        >
          排行榜
        </button>
      </div>

      {/* 內容切換容器 */}
      <div className="w-full max-w-[600px] mt-4">
        {/* 遊戲說明 */}
        {gameState.showInstructions && (
          <div className="w-full mx-auto mb-4 p-5 bg-black bg-opacity-75 border-3 border-yellow-400 shadow-[0_0_10px_rgba(255,255,0,0.2)]">
            <h3 className="text-yellow-400 mb-4 text-lg md:text-xl text-shadow-black">
              遊戲說明
            </h3>
            <ul className="list-none leading-relaxed text-sm md:text-base">
              <li className="my-2.5 pl-6 relative">
                <span className="absolute left-0 text-yellow-400 font-bold">&gt;&gt;</span>
                使用 WASD 或方向鍵控制小精靈移動
              </li>
              <li className="my-2.5 pl-6 relative">
                <span className="absolute left-0 text-yellow-400 font-bold">&gt;&gt;</span>
                空白鍵暫停遊戲
              </li>
              <li className="my-2.5 pl-6 relative">
                <span className="absolute left-0 text-yellow-400 font-bold">&gt;&gt;</span>
                收集黃色點數 (20分) 和大力丸 (50分)
              </li>
              <li className="my-2.5 pl-6 relative">
                <span className="absolute left-0 text-yellow-400 font-bold">&gt;&gt;</span>
                吃大力丸後可以擊殺鬼怪 (150分)
              </li>
              <li className="my-2.5 pl-6 relative">
                <span className="absolute left-0 text-yellow-400 font-bold">&gt;&gt;</span>
                避免被鬼怪抓到，你有 3 條命
              </li>
              <li className="my-2.5 pl-6 relative">
                <span className="absolute left-0 text-yellow-400 font-bold">&gt;&gt;</span>
                10分鐘內收集完所有點數晉級下一關
              </li>
            </ul>
          </div>
        )}

        {/* 排行榜 */}
        {gameState.showLeaderboard && (
          <div className="w-full mx-auto mb-4 p-5 bg-black bg-opacity-75 border-3 border-yellow-400 shadow-[0_0_10px_rgba(255,255,0,0.2)]">
            <h3 className="text-yellow-400 mb-4 text-lg md:text-xl text-shadow-black">
              排行榜
            </h3>
            <ol className="list-none p-0 text-sm md:text-base">
              {gameState.leaderboard.length > 0 ? (
                gameState.leaderboard.slice(0, 10).map((entry, index) => (
                  <li
                    key={index}
                    className="py-2 px-2.5 my-1.5 bg-blue-900 bg-opacity-50 border-l-4 border-yellow-400 relative"
                  >
                    <span className="font-bold text-yellow-400 mr-2.5">
                      {index + 1}.
                    </span>
                    {entry.name} - {entry.score}分
                  </li>
                ))
              ) : (
                <li className="py-2 px-2.5 my-1.5 bg-blue-900 bg-opacity-50 border-l-4 border-yellow-400">
                  暫無記錄
                </li>
              )}
            </ol>
          </div>
        )}
      </div>
    </div>
  );
}

export default StartScreen;
