import React from 'react';
import { useGame, GAME_ACTIONS } from '../contexts/GameContext';

function PauseScreen() {
  const { dispatch } = useGame();

  const handleResumeGame = () => {
    dispatch({ type: GAME_ACTIONS.SET_PAUSED, payload: false });
    dispatch({ type: GAME_ACTIONS.SET_CURRENT_SCREEN, payload: 'game' });
  };

  const handleBackToMenu = () => {
    dispatch({ type: GAME_ACTIONS.SET_PAUSED, payload: false });
    dispatch({ type: GAME_ACTIONS.SET_CURRENT_SCREEN, payload: 'start' });
    dispatch({ type: GAME_ACTIONS.RESET_GAME });
  };

  return (
    <div className="absolute inset-0 flex flex-col justify-center items-center p-5 text-center bg-blue-900 bg-opacity-92 border-4 border-orange-400 shadow-[0_0_25px_#FFB84D,inset_0_0_20px_rgba(0,0,0,0.5)] z-[2500] text-white">
      {/* 暫停標題 */}
      <h2 className="text-4xl md:text-6xl lg:text-7xl text-red-500 mb-10 animate-pulse">
        <span className="text-shadow-[3px_3px_0px_#000000,0_0_8px_#fff,0_0_12px_#FF0000,0_0_18px_#FFB84D]">
          遊戲暫停
        </span>
      </h2>

      {/* 按鈕組 */}
      <div className="flex flex-col items-center space-y-4">
        <button
          onClick={handleResumeGame}
          className="pacman-pixel-button"
        >
          繼續遊戲
        </button>
        
        <button
          onClick={handleBackToMenu}
          className="pacman-pixel-button"
        >
          回到主選單
        </button>
      </div>

      {/* 提示文字 */}
      <div className="mt-8 text-sm text-gray-300">
        <p>按空白鍵也可以繼續遊戲</p>
      </div>
    </div>
  );
}

export default PauseScreen;
