import { useEffect, useRef, useState } from 'react';
import * as Tone from 'tone';

export function useAudio() {
  const [isReady, setIsReady] = useState(false);
  const synthsRef = useRef({});
  const sequencesRef = useRef({});

  // 初始化音效系統
  useEffect(() => {
    const initAudio = async () => {
      try {
        // 確保 Tone.js 上下文已啟動
        if (Tone.context.state !== 'running') {
          await Tone.start();
        }

        // 創建合成器
        synthsRef.current = {
          // 小精靈移動音效
          pacmanMove: new Tone.Oscillator({
            frequency: 440,
            type: 'square'
          }).toDestination(),

          // 吃點數音效
          dotEat: new Tone.Oscillator({
            frequency: 880,
            type: 'sine'
          }).toDestination(),

          // 吃大力丸音效
          powerPelletEat: new Tone.Oscillator({
            frequency: 220,
            type: 'sawtooth'
          }).toDestination(),

          // 吃鬼怪音效
          ghostEat: new Tone.Oscillator({
            frequency: 660,
            type: 'triangle'
          }).toDestination(),

          // 死亡音效
          death: new Tone.Oscillator({
            frequency: 110,
            type: 'square'
          }).toDestination(),

          // 遊戲開始音效
          gameStart: new Tone.Oscillator({
            frequency: 330,
            type: 'sine'
          }).toDestination()
        };

        setIsReady(true);
        console.log('Audio system initialized');
      } catch (error) {
        console.error('Failed to initialize audio system:', error);
      }
    };

    initAudio();

    // 清理函數
    return () => {
      Object.values(synthsRef.current).forEach(synth => {
        if (synth && synth.dispose) {
          synth.dispose();
        }
      });
      Object.values(sequencesRef.current).forEach(sequence => {
        if (sequence && sequence.dispose) {
          sequence.dispose();
        }
      });
    };
  }, []);

  // 播放點數音效
  const playDotSound = () => {
    if (!isReady || !synthsRef.current.dotEat) return;
    
    try {
      const synth = synthsRef.current.dotEat;
      synth.frequency.setValueAtTime(880, Tone.now());
      synth.start(Tone.now());
      synth.stop(Tone.now() + 0.1);
    } catch (error) {
      console.warn('Failed to play dot sound:', error);
    }
  };

  // 播放大力丸音效
  const playPowerPelletSound = () => {
    if (!isReady || !synthsRef.current.powerPelletEat) return;
    
    try {
      const synth = synthsRef.current.powerPelletEat;
      synth.frequency.setValueAtTime(220, Tone.now());
      synth.start(Tone.now());
      synth.stop(Tone.now() + 0.3);
    } catch (error) {
      console.warn('Failed to play power pellet sound:', error);
    }
  };

  // 播放吃鬼怪音效
  const playEatGhostSound = () => {
    if (!isReady || !synthsRef.current.ghostEat) return;
    
    try {
      const synth = synthsRef.current.ghostEat;
      synth.frequency.setValueAtTime(660, Tone.now());
      synth.start(Tone.now());
      synth.stop(Tone.now() + 0.2);
    } catch (error) {
      console.warn('Failed to play eat ghost sound:', error);
    }
  };

  // 播放死亡音效
  const playDeathSound = () => {
    if (!isReady || !synthsRef.current.death) return;
    
    try {
      const synth = synthsRef.current.death;
      const now = Tone.now();
      
      // 創建下降音調效果
      synth.frequency.setValueAtTime(440, now);
      synth.frequency.exponentialRampToValueAtTime(110, now + 1);
      synth.start(now);
      synth.stop(now + 1);
    } catch (error) {
      console.warn('Failed to play death sound:', error);
    }
  };

  // 播放遊戲開始音效
  const playStartSound = () => {
    if (!isReady || !synthsRef.current.gameStart) return;
    
    try {
      const synth = synthsRef.current.gameStart;
      const now = Tone.now();
      
      // 創建上升音調效果
      synth.frequency.setValueAtTime(220, now);
      synth.frequency.exponentialRampToValueAtTime(440, now + 0.5);
      synth.start(now);
      synth.stop(now + 0.5);
    } catch (error) {
      console.warn('Failed to play start sound:', error);
    }
  };

  // 播放小精靈移動音效
  const playMoveSound = () => {
    if (!isReady || !synthsRef.current.pacmanMove) return;
    
    try {
      const synth = synthsRef.current.pacmanMove;
      synth.frequency.setValueAtTime(440, Tone.now());
      synth.start(Tone.now());
      synth.stop(Tone.now() + 0.05);
    } catch (error) {
      console.warn('Failed to play move sound:', error);
    }
  };

  // 播放背景音樂（簡化版）
  const playBackgroundMusic = () => {
    if (!isReady) return;
    
    try {
      // 創建簡單的背景音樂序列
      const synth = new Tone.Synth().toDestination();
      const melody = ['C4', 'E4', 'G4', 'C5', 'G4', 'E4'];
      
      const sequence = new Tone.Sequence((time, note) => {
        synth.triggerAttackRelease(note, '8n', time);
      }, melody, '4n');

      sequence.start(0);
      Tone.Transport.start();

      sequencesRef.current.background = sequence;
    } catch (error) {
      console.warn('Failed to play background music:', error);
    }
  };

  // 停止背景音樂
  const stopBackgroundMusic = () => {
    try {
      if (sequencesRef.current.background) {
        sequencesRef.current.background.stop();
        sequencesRef.current.background.dispose();
        delete sequencesRef.current.background;
      }
      Tone.Transport.stop();
    } catch (error) {
      console.warn('Failed to stop background music:', error);
    }
  };

  // 設置音量
  const setVolume = (volume) => {
    try {
      Tone.Destination.volume.value = Tone.gainToDb(volume);
    } catch (error) {
      console.warn('Failed to set volume:', error);
    }
  };

  // 靜音/取消靜音
  const toggleMute = () => {
    try {
      Tone.Destination.mute = !Tone.Destination.mute;
      return Tone.Destination.mute;
    } catch (error) {
      console.warn('Failed to toggle mute:', error);
      return false;
    }
  };

  return {
    isReady,
    playDotSound,
    playPowerPelletSound,
    playEatGhostSound,
    playDeathSound,
    playStartSound,
    playMoveSound,
    playBackgroundMusic,
    stopBackgroundMusic,
    setVolume,
    toggleMute
  };
}
