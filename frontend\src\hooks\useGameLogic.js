import { useEffect, useRef, useCallback } from 'react';
import { useGame, GAME_ACTIONS, GAME_CONSTANTS } from '../contexts/GameContext';
import { fetchRoadData, generateRoadNetwork, findNearestRoadPosition, drawVisualRoads, positionsAreEqual } from '../utils/mapUtils';
import { decideGhostMove, aStarSearch, generateGhostSpawnPoints, checkCollision, getNeighbors } from '../utils/aiUtils';
import { useAudio } from './useAudio';
import L from 'leaflet';

export function useGameLogic() {
  const { gameState, dispatch } = useGame();
  const { playDotSound, playPowerPelletSound, playEatGhostSound, playDeathSound, playStartSound } = useAudio();
  const gameLoopRef = useRef(null);
  const lastFrameTimeRef = useRef(0);

  // 初始化遊戲
  const initGame = useCallback(async (mapInstance) => {
    if (!mapInstance) return;

    console.log('Initializing game...');
    
    // 重置遊戲狀態
    dispatch({ type: GAME_ACTIONS.RESET_GAME });
    dispatch({ type: GAME_ACTIONS.SET_MAP, payload: mapInstance });

    try {
      // 獲取地圖邊界
      const bounds = mapInstance.getBounds();
      
      // 獲取道路數據
      const roadData = await fetchRoadData(bounds);
      if (!roadData) {
        console.error('Failed to fetch road data');
        return;
      }

      // 生成道路網絡
      const { validPositions, roadNetwork, adjacencyList } = await generateRoadNetwork(bounds, roadData);
      
      if (validPositions.length === 0) {
        console.error('No valid positions generated');
        return;
      }

      // 更新遊戲狀態
      dispatch({ type: GAME_ACTIONS.SET_VALID_POSITIONS, payload: validPositions });
      dispatch({ type: GAME_ACTIONS.SET_ROAD_NETWORK, payload: roadNetwork });
      dispatch({ type: GAME_ACTIONS.SET_ADJACENCY_LIST, payload: adjacencyList });

      // 繪製道路
      const roadLayers = drawVisualRoads(mapInstance, roadNetwork);

      // 初始化遊戲元素
      await initGameElements(mapInstance, validPositions, adjacencyList);

      dispatch({ type: GAME_ACTIONS.SET_CAN_MOVE, payload: true });
      
      console.log('Game initialized successfully');
    } catch (error) {
      console.error('Error initializing game:', error);
    }
  }, [dispatch]);

  // 初始化遊戲元素（小精靈、鬼怪、點數）
  const initGameElements = useCallback(async (mapInstance, validPositions, adjacencyList) => {
    if (!mapInstance || !validPositions || validPositions.length === 0) return;

    // 創建小精靈
    const pacmanStartPosition = validPositions[Math.floor(Math.random() * validPositions.length)];
    const pacmanIcon = L.divIcon({
      className: 'pacman-icon',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });
    
    const pacman = L.marker(pacmanStartPosition, { icon: pacmanIcon }).addTo(mapInstance);
    dispatch({ type: GAME_ACTIONS.SET_PACMAN, payload: pacman });

    // 生成鬼怪出生點
    const ghostSpawnPoints = generateGhostSpawnPoints(validPositions, GAME_CONSTANTS.NUMBER_OF_GHOSTS);
    dispatch({ type: GAME_ACTIONS.SET_GHOST_SPAWN_POINTS, payload: ghostSpawnPoints });

    // 創建鬼怪
    const ghosts = [];
    const ghostColors = ['red', 'pink', 'cyan', 'orange', 'purple', 'green', 'blue'];
    
    for (let i = 0; i < Math.min(ghostSpawnPoints.length, GAME_CONSTANTS.NUMBER_OF_GHOSTS); i++) {
      const ghostIcon = L.divIcon({
        className: `ghost-icon ghost-${ghostColors[i % ghostColors.length]}`,
        iconSize: [20, 20],
        iconAnchor: [10, 10],
        html: '<div class="wave1"></div><div class="wave2"></div><div class="wave3"></div>'
      });
      
      const ghost = L.marker(ghostSpawnPoints[i], { icon: ghostIcon }).addTo(mapInstance);
      ghosts.push(ghost);
    }
    
    dispatch({ type: GAME_ACTIONS.SET_GHOSTS, payload: ghosts });

    // 生成點數和大力丸
    generateDots(mapInstance, validPositions, pacmanStartPosition);
  }, [dispatch]);

  // 生成點數和大力丸
  const generateDots = useCallback((mapInstance, validPositions, pacmanPosition) => {
    if (!mapInstance || !validPositions || validPositions.length === 0) return;

    // 清除現有的點數
    // 這裡需要實現清除邏輯

    const dots = [];
    const powerPellets = [];
    
    // 過濾掉小精靈附近的位置
    const availablePositions = validPositions.filter(pos => {
      const distance = L.latLng(pos[0], pos[1]).distanceTo(L.latLng(pacmanPosition[0], pacmanPosition[1]));
      return distance > 50; // 50米以外
    });

    // 生成普通點數（每10個位置放一個）
    for (let i = 0; i < availablePositions.length; i += 10) {
      const position = availablePositions[i];
      const dotIcon = L.divIcon({
        className: 'dot',
        iconSize: [4, 4],
        iconAnchor: [2, 2]
      });
      
      const dot = L.marker(position, { icon: dotIcon }).addTo(mapInstance);
      dots.push(dot);
    }

    // 生成大力丸（每100個位置放一個）
    for (let i = 0; i < availablePositions.length; i += 100) {
      const position = availablePositions[i];
      const pelletIcon = L.divIcon({
        className: 'power-pellet',
        iconSize: [12, 12],
        iconAnchor: [6, 6]
      });
      
      const pellet = L.marker(position, { icon: pelletIcon }).addTo(mapInstance);
      powerPellets.push(pellet);
    }

    dispatch({ type: GAME_ACTIONS.SET_DOTS, payload: dots });
    dispatch({ type: GAME_ACTIONS.SET_POWER_PELLETS, payload: powerPellets });
  }, [dispatch]);

  // 遊戲主循環
  const gameLoop = useCallback((currentTime) => {
    if (!gameState.canMove || gameState.isPaused || gameState.isGameOver) {
      gameLoopRef.current = requestAnimationFrame(gameLoop);
      return;
    }

    const deltaTime = Math.min(currentTime - lastFrameTimeRef.current, GAME_CONSTANTS.MAX_DELTA_TIME);
    lastFrameTimeRef.current = currentTime;

    // 更新小精靈移動
    updatePacmanMovement(deltaTime);

    // 更新鬼怪移動
    updateGhostMovement(deltaTime);

    // 檢查碰撞
    checkCollisions();

    // 更新遊戲計時器
    updateGameTimer(deltaTime);

    gameLoopRef.current = requestAnimationFrame(gameLoop);
  }, [gameState.canMove, gameState.isPaused, gameState.isGameOver, updatePacmanMovement, updateGhostMovement, checkCollisions, updateGameTimer]);

  // 更新小精靈移動
  const updatePacmanMovement = useCallback((deltaTime) => {
    if (!gameState.pacman || !gameState.pacmanMovement.isMoving) return;

    const movement = gameState.pacmanMovement;
    const speed = GAME_CONSTANTS.PACMAN_BASE_SPEED * gameState.pacmanSpeedMultiplier;
    const distanceToMove = (speed * deltaTime) / 1000;

    const newDistanceTraveled = movement.distanceTraveledThisSegment + distanceToMove;

    if (newDistanceTraveled >= movement.totalDistanceToDestinationNode) {
      // 到達目標節點
      gameState.pacman.setLatLng(movement.destinationNodeLatLng);
      dispatch({
        type: GAME_ACTIONS.UPDATE_PACMAN_MOVEMENT,
        payload: {
          isMoving: false,
          distanceTraveledThisSegment: 0
        }
      });
    } else {
      // 繼續移動
      const progress = newDistanceTraveled / movement.totalDistanceToDestinationNode;
      const currentLat = movement.startPositionLatLng.lat + 
        (movement.destinationNodeLatLng.lat - movement.startPositionLatLng.lat) * progress;
      const currentLng = movement.startPositionLatLng.lng + 
        (movement.destinationNodeLatLng.lng - movement.startPositionLatLng.lng) * progress;

      gameState.pacman.setLatLng([currentLat, currentLng]);
      dispatch({
        type: GAME_ACTIONS.UPDATE_PACMAN_MOVEMENT,
        payload: { distanceTraveledThisSegment: newDistanceTraveled }
      });
    }
  }, [gameState, dispatch]);

  // 更新鬼怪移動
  const updateGhostMovement = useCallback((deltaTime) => {
    // 簡化的鬼怪移動邏輯
    // 實際實現會更複雜
  }, [gameState, dispatch]);

  // 檢查碰撞
  const checkCollisions = useCallback(() => {
    if (!gameState.pacman || !gameState.dots || !gameState.powerPellets || !gameState.ghosts) return;

    const pacmanPos = gameState.pacman.getLatLng();

    // 檢查點數碰撞
    gameState.dots.forEach((dot, index) => {
      const dotPos = dot.getLatLng();
      if (checkCollision([pacmanPos.lat, pacmanPos.lng], [dotPos.lat, dotPos.lng], 0.0001)) {
        gameState.map.removeLayer(dot);
        dispatch({ type: GAME_ACTIONS.COLLECT_DOT, payload: index });
        playDotSound();
      }
    });

    // 檢查大力丸碰撞
    gameState.powerPellets.forEach((pellet, index) => {
      const pelletPos = pellet.getLatLng();
      if (checkCollision([pacmanPos.lat, pacmanPos.lng], [pelletPos.lat, pelletPos.lng], 0.0001)) {
        gameState.map.removeLayer(pellet);
        dispatch({ type: GAME_ACTIONS.COLLECT_POWER_PELLET, payload: index });
        playPowerPelletSound();
      }
    });

    // 檢查鬼怪碰撞
    if (!gameState.godMode) {
      gameState.ghosts.forEach(ghost => {
        const ghostPos = ghost.getLatLng();
        if (checkCollision([pacmanPos.lat, pacmanPos.lng], [ghostPos.lat, ghostPos.lng], 0.0001)) {
          if (gameState.powerMode) {
            // 吃掉鬼怪
            dispatch({ type: GAME_ACTIONS.UPDATE_SCORE, payload: gameState.score + 150 });
            playEatGhostSound();
          } else {
            // 失去生命
            dispatch({ type: GAME_ACTIONS.UPDATE_LIVES, payload: gameState.lives - 1 });
            dispatch({ type: GAME_ACTIONS.SET_LOSING_LIFE, payload: true });
            playDeathSound();
          }
        }
      });
    }
  }, [gameState, dispatch]);

  // 更新遊戲計時器
  const updateGameTimer = useCallback((deltaTime) => {
    const newTime = Math.max(0, gameState.gameTime - deltaTime / 1000);
    dispatch({ type: GAME_ACTIONS.UPDATE_GAME_TIME, payload: newTime });

    if (newTime <= 0) {
      dispatch({ type: GAME_ACTIONS.SET_GAME_OVER, payload: true });
      dispatch({ type: GAME_ACTIONS.SET_CURRENT_SCREEN, payload: 'gameOver' });
    }
  }, [gameState.gameTime, dispatch]);

  // 開始遊戲循環
  const startGameLoop = useCallback(() => {
    if (gameLoopRef.current) {
      cancelAnimationFrame(gameLoopRef.current);
    }
    lastFrameTimeRef.current = performance.now();
    gameLoopRef.current = requestAnimationFrame(gameLoop);
  }, [gameLoop]);

  // 停止遊戲循環
  const stopGameLoop = useCallback(() => {
    if (gameLoopRef.current) {
      cancelAnimationFrame(gameLoopRef.current);
      gameLoopRef.current = null;
    }
  }, []);

  // 清理效果
  useEffect(() => {
    return () => {
      stopGameLoop();
    };
  }, [stopGameLoop]);

  // 嘗試開始小精靈移動
  const tryStartMovement = useCallback((directionKey) => {
    if (!gameState.pacman || !gameState.validPositions || !gameState.adjacencyList || gameState.pacmanMovement.isMoving) {
      return;
    }

    const currentPos = gameState.pacman.getLatLng();
    const currentNode = findNearestRoadPosition(currentPos.lat, currentPos.lng, gameState.validPositions);
    const neighbors = getNeighbors(currentNode, gameState.adjacencyList);

    if (neighbors.length === 0) return;

    // 根據方向鍵選擇目標節點
    let targetNode = null;
    const directionMap = {
      'KeyW': 'up',
      'KeyS': 'down',
      'KeyA': 'left',
      'KeyD': 'right'
    };

    const direction = directionMap[directionKey];
    if (!direction) return;

    // 找到最符合方向的鄰居節點
    let bestNeighbor = null;
    let bestScore = -Infinity;

    for (const neighbor of neighbors) {
      const deltaLat = neighbor[0] - currentNode[0];
      const deltaLng = neighbor[1] - currentNode[1];
      let score = 0;

      switch (direction) {
        case 'up':
          score = deltaLat;
          break;
        case 'down':
          score = -deltaLat;
          break;
        case 'left':
          score = -deltaLng;
          break;
        case 'right':
          score = deltaLng;
          break;
      }

      if (score > bestScore) {
        bestScore = score;
        bestNeighbor = neighbor;
      }
    }

    if (bestNeighbor && bestScore > 0) {
      const startLatLng = L.latLng(currentNode[0], currentNode[1]);
      const destLatLng = L.latLng(bestNeighbor[0], bestNeighbor[1]);
      const distance = startLatLng.distanceTo(destLatLng);

      dispatch({
        type: GAME_ACTIONS.UPDATE_PACMAN_MOVEMENT,
        payload: {
          isMoving: true,
          startPositionLatLng: startLatLng,
          destinationNodeLatLng: destLatLng,
          totalDistanceToDestinationNode: distance,
          distanceTraveledThisSegment: 0,
          currentFacingDirection: direction
        }
      });
    }
  }, [gameState, dispatch]);

  return {
    initGame,
    startGameLoop,
    stopGameLoop,
    generateDots,
    tryStartMovement
  };
}
