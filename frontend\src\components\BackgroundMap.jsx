import { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer } from 'react-leaflet';
import { MAP_CONFIGS } from '../contexts/GameContext';
import 'leaflet/dist/leaflet.css';

function BackgroundMap() {
  const mapRef = useRef(null);
  
  // 使用第一個地圖作為背景
  const backgroundMapConfig = MAP_CONFIGS[0];

  useEffect(() => {
    // 這裡會初始化背景動畫
    console.log('BackgroundMap mounted');
  }, []);

  return (
    <div className="absolute inset-0 w-full h-full z-[998] bg-black opacity-35 grayscale-[0.1] transition-opacity duration-500 ease-in-out">
      <MapContainer
        ref={mapRef}
        center={backgroundMapConfig.center}
        zoom={backgroundMapConfig.zoom}
        minZoom={backgroundMapConfig.zoom}
        maxZoom={backgroundMapConfig.zoom}
        zoomControl={false}
        attributionControl={false}
        scrollWheelZoom={false}
        dragging={false}
        touchZoom={false}
        doubleClickZoom={false}
        className="w-full h-full"
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          maxZoom={backgroundMapConfig.zoom + 1}
        />
      </MapContainer>
    </div>
  );
}

export default BackgroundMap;
