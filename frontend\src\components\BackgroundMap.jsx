import { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ile<PERSON>ayer } from 'react-leaflet';
import { MAP_CONFIGS } from '../contexts/GameContext';
import L from 'leaflet';

// 修復 Leaflet 預設圖標問題
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

function BackgroundMap() {
  const mapRef = useRef(null);
  
  // 使用第一個地圖作為背景
  const backgroundMapConfig = MAP_CONFIGS[0];

  useEffect(() => {
    // 這裡會初始化背景動畫
    console.log('BackgroundMap mounted');
    console.log('Map config:', backgroundMapConfig);
  }, [backgroundMapConfig]);

  try {
    return (
      <div className="absolute inset-0 w-full h-full z-[998] bg-gray-800 opacity-50 transition-opacity duration-500 ease-in-out">
        <MapContainer
          ref={mapRef}
          center={backgroundMapConfig.center}
          zoom={backgroundMapConfig.zoom}
          minZoom={backgroundMapConfig.zoom}
          maxZoom={backgroundMapConfig.zoom}
          zoomControl={false}
          attributionControl={false}
          scrollWheelZoom={false}
          dragging={false}
          touchZoom={false}
          doubleClickZoom={false}
          className="w-full h-full"
        >
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            maxZoom={backgroundMapConfig.zoom + 1}
          />
        </MapContainer>
      </div>
    );
  } catch (error) {
    console.error('BackgroundMap error:', error);
    return (
      <div className="absolute inset-0 w-full h-full z-[998] bg-gray-800 opacity-50 flex items-center justify-center">
        <p className="text-white">地圖載入中...</p>
      </div>
    );
  }
}

export default BackgroundMap;
