import { TNativeAudioContextConstructor } from './native-audio-context-constructor';
import { TNativeChannelMergerNodeFactory } from './native-channel-merger-node-factory';
import { TWrapChannelMergerNodeFunction } from './wrap-channel-merger-node-function';

export type TNativeChannelMergerNodeFactoryFactory = (
    nativeAudioContextConstructor: null | TNativeAudioContextConstructor,
    wrapChannelMergerNode: TWrapChannelMergerNodeFunction
) => TNativeChannelMergerNodeFactory;
