import { TNativeStereoPannerNodeFactory } from './native-stereo-panner-node-factory';
import { TNativeStereoPannerNodeFakerFactory } from './native-stereo-panner-node-faker-factory';
import { TNotSupportedErrorFactory } from './not-supported-error-factory';
export type TNativeStereoPannerNodeFactoryFactory = (createNativeStereoPannerNodeFaker: TNativeStereoPannerNodeFakerFactory, createNotSupportedError: TNotSupportedErrorFactory) => TNativeStereoPannerNodeFactory;
//# sourceMappingURL=native-stereo-panner-node-factory-factory.d.ts.map