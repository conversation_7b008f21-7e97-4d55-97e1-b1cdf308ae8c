import { TInsertElementInSetFunction } from './insert-element-in-set-function';
import { TIsNativeAudioNodeFunction } from './is-native-audio-node-function';
import { TMonitorConnectionsFunction } from './monitor-connections-function';

export type TMonitorConnectionsFactory = (
    insertElementInSet: TInsertElementInSetFunction,
    isNativeAudioNode: TIsNativeAudioNodeFunction
) => TMonitorConnectionsFunction;
