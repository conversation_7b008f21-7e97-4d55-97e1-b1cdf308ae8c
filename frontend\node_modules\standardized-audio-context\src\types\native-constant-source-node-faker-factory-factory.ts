import { TAddSilentConnectionFunction } from './add-silent-connection-function';
import { TMonitorConnectionsFunction } from './monitor-connections-function';
import { TNativeAudioBufferSourceNodeFactory } from './native-audio-buffer-source-node-factory';
import { TNativeConstantSourceNodeFakerFactory } from './native-constant-source-node-faker-factory';
import { TNativeGainNodeFactory } from './native-gain-node-factory';

export type TNativeConstantSourceNodeFakerFactoryFactory = (
    addSilentConnection: TAddSilentConnectionFunction,
    createNativeAudioBufferSourceNode: TNativeAudioBufferSourceNodeFactory,
    createNativeGainNode: TNativeGainNodeFactory,
    monitorConnections: TMonitorConnectionsFunction
) => TNativeConstantSourceNodeFakerFactory;
